{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?083b", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?c91f", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?25c0", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?86b1", "uni-app:///src/pages/lanhu_huiyuanjiemian/index.vue", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?8a8c", "webpack:///./src/pages/lanhu_huiyuanjiemian/index.vue?d4b9"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAyD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFtCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCmPtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC1PA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_huiyuanjiemian/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_huiyuanjiemian/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=ab378642&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_huiyuanjiemian/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=ab378642&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"box_1 flex-row\">\n      <view class=\"group_1 flex-col\">\n        <view class=\"box_2 flex-row\">\n          <text class=\"text_1\">12:30</text>\n          <image\n            class=\"thumbnail_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNGd123943771561d022a87b263425c4041.png\"\n          />\n          <image\n            class=\"thumbnail_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG2e598a65d05619bcca910a84029f0065.png\"\n          />\n          <image\n            class=\"thumbnail_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG2766341d0b27d2f2376709071239a4d2.png\"\n          />\n        </view>\n        <view class=\"box_3 flex-col\">\n          <view class=\"image-wrapper_1 flex-col align-center\">\n            <image\n              class=\"image_1\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG0752dc5e68972b129f049b8c4a922653.png\"\n            />\n          </view>\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG6ed4604c1b39778be5110bf07faac9d5.png\"\n          />\n        </view>\n        <view class=\"text-wrapper_1 flex-row\">\n          <text class=\"text_2\">V1</text>\n          <text class=\"text_3\">V2</text>\n          <text class=\"text_4\">V3</text>\n          <text class=\"text_5\">V4</text>\n        </view>\n        <view class=\"box_4 flex-col\">\n          <view class=\"text-wrapper_2 flex-row\">\n            <text class=\"text_6\">会员权益</text>\n          </view>\n          <view class=\"group_2 flex-row justify-between\">\n            <view class=\"group_3 flex-col\"></view>\n            <view class=\"group_4 flex-col\"></view>\n            <view class=\"group_5 flex-col\"></view>\n            <view class=\"group_6 flex-col\"></view>\n          </view>\n          <view class=\"group_7 flex-row\">\n            <view class=\"text-wrapper_3\">\n              <text class=\"paragraph_1\">\n                会员88折\n                <br />\n              </text>\n              <text class=\"text_7\">性价比高</text>\n            </view>\n            <view class=\"text-wrapper_4\">\n              <text class=\"paragraph_2\">\n                专属客服\n                <br />\n              </text>\n              <text class=\"text_8\">一对一服务</text>\n            </view>\n            <view class=\"text-wrapper_5\">\n              <text class=\"paragraph_3\">\n                极速退款\n                <br />\n              </text>\n              <text class=\"text_9\">快速反馈</text>\n            </view>\n            <view class=\"text-wrapper_6\">\n              <text class=\"paragraph_4\">\n                高效派单\n                <br />\n              </text>\n              <text class=\"text_10\">优质人员</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"box_5 flex-col\">\n          <view class=\"text-wrapper_7 flex-row\">\n            <text class=\"text_11\">下单任务</text>\n          </view>\n          <view class=\"group_8 flex-row\">\n            <view class=\"image-wrapper_2 flex-col\">\n              <image\n                class=\"label_1\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG99b9e95eecb2cdb593790d072e05eece.png\"\n              />\n            </view>\n            <view class=\"text-group_1 flex-col justify-between\">\n              <text class=\"text_12\">服务项目</text>\n              <view class=\"text-wrapper_8\">\n                <text class=\"text_13\">每消费1元，1成长值</text>\n                <text class=\"text_14\">+233</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_9 flex-col\">\n              <text class=\"text_15\">去下单</text>\n            </view>\n          </view>\n          <view class=\"group_9 flex-row\">\n            <image\n              class=\"label_2\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG6048b9e6357fc6f3bffd2582a1f07cba.png\"\n            />\n            <view class=\"text-group_2 flex-col justify-between\">\n              <text class=\"text_16\">车费</text>\n              <view class=\"text-wrapper_10\">\n                <text class=\"text_17\">每消费1元，1成长值</text>\n                <text class=\"text_18\">+233</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_11 flex-col\">\n              <text class=\"text_19\">去下单</text>\n            </view>\n          </view>\n          <view class=\"group_10 flex-row\">\n            <view class=\"group_11 flex-col\">\n              <view class=\"group_12 flex-col\"></view>\n            </view>\n            <view class=\"text-group_3 flex-col justify-between\">\n              <text class=\"text_20\">加钟</text>\n              <view class=\"text-wrapper_12\">\n                <text class=\"text_21\">每消费1元，1成长值</text>\n                <text class=\"text_22\">+233</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_13 flex-col\">\n              <text class=\"text_23\">去下单</text>\n            </view>\n          </view>\n          <view class=\"group_13 flex-row\">\n            <view class=\"image-wrapper_3 flex-col\">\n              <image\n                class=\"label_3\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNGdbd8f3263a314cd6106ef7cbd4715bf8.png\"\n              />\n            </view>\n            <view class=\"text-group_4 flex-col justify-between\">\n              <text class=\"text_24\">余额充值</text>\n              <view class=\"text-wrapper_14\">\n                <text class=\"text_25\">每消费1元，1成长值</text>\n                <text class=\"text_26\">+233</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_15 flex-col\">\n              <text class=\"text_27\">去下单</text>\n            </view>\n          </view>\n        </view>\n        <view class=\"box_6 flex-col\">\n          <view class=\"box_7 flex-col\">\n            <view class=\"box_8 flex-row\">\n              <view class=\"image-wrapper_4 flex-col\">\n                <image\n                  class=\"label_4\"\n                  referrerpolicy=\"no-referrer\"\n                  src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG264530a3cd2f5356002b820af0e997a2.png\"\n                />\n              </view>\n              <view class=\"text-group_5 flex-col justify-between\">\n                <text class=\"text_28\">星空的夜晚</text>\n                <text class=\"text_29\">2022-11-05到期</text>\n              </view>\n              <view class=\"box_9 flex-col\"></view>\n              <view class=\"box_10 flex-col\">\n                <view class=\"box_11 flex-col\"></view>\n              </view>\n              <view class=\"box_12 flex-col\"></view>\n            </view>\n            <view class=\"box_13 flex-row\">\n              <text class=\"text_30\">还差21231242成长值升级至至高会员</text>\n              <image\n                class=\"image_2\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/lanhu_huiyuanjiemian/3104b22ef2924cb0b80dd159fab66b56_mergeImage.png\"\n              />\n              <view class=\"group_14 flex-col\">\n                <view class=\"group_15 flex-col\"></view>\n              </view>\n            </view>\n            <view class=\"box_14 flex-row\">\n              <view class=\"image-text_1 flex-col justify-between\">\n                <image\n                  class=\"image_3\"\n                  referrerpolicy=\"no-referrer\"\n                  src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG0ea5a50c88cdd2e188988aeceeba7f9d.png\"\n                />\n                <text class=\"text-group_6\">2132121/23132133</text>\n              </view>\n              <text class=\"text_31\">V1</text>\n              <view class=\"section_1 flex-col\"></view>\n            </view>\n            <view class=\"box_15 flex-col\"></view>\n            <view class=\"box_16 flex-col\">\n              <view class=\"group_16 flex-col\">\n                <view class=\"group_17 flex-col\">\n                  <view class=\"box_17 flex-col\"></view>\n                  <view class=\"box_18 flex-col\"></view>\n                </view>\n                <view class=\"group_18 flex-col\"></view>\n              </view>\n            </view>\n            <view class=\"box_19 flex-col\">\n              <view class=\"group_19 flex-col\"></view>\n              <view class=\"group_20 flex-col\"></view>\n            </view>\n            <view class=\"box_20 flex-col\">\n              <view class=\"group_21 flex-col\">\n                <view class=\"group_22 flex-col\"></view>\n                <view class=\"group_23 flex-col\"></view>\n              </view>\n            </view>\n            <view class=\"box_21 flex-col\"></view>\n          </view>\n        </view>\n        <text class=\"text_32\">开通会员</text>\n        <image\n          class=\"image_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_huiyuanjiemian/FigmaDDSSlicePNG85b2c25217aac922623185782f643c68.png\"\n        />\n        <view class=\"box_22 flex-col\">\n          <view class=\"box_23 flex-col\"></view>\n        </view>\n        <view class=\"box_24 flex-col\"></view>\n        <view class=\"box_25 flex-col\"></view>\n        <view class=\"box_26 flex-col\"></view>\n        <view class=\"box_27 flex-col\"></view>\n      </view>\n      <view class=\"group_24 flex-col\"></view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177583026\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}