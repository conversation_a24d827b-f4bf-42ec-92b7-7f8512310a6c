{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?8303", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?4b48", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?a06d", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?631f", "uni-app:///src/pages/lanhu_shangjiaxiangqing_1/index.vue", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?fec3", "webpack:///./src/pages/lanhu_shangjiaxiangqing_1/index.vue?838c"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhuimage0", "lanhutext0", "lanhutext1", "lanhutext2", "lanhutext3", "lanhutext4", "lanhutext5", "lanhutext6", "lanhutext7", "lanhutext8", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA8D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF3CG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC2Jtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA,GACA;QACAT,WAAA,EACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC9LA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shangjiaxiangqing_1/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shangjiaxiangqing_1/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=263c1f20&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shangjiaxiangqing_1/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=263c1f20&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col justify-between\">\n      <view class=\"block_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"block_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">服务详情</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <image\n      class=\"image_2\"\n      referrerpolicy=\"no-referrer\"\n      src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNGfab0fe2d3d976c693c7d16d3d3368744.png\"\n    />\n    <view class=\"group_2 flex-col\">\n      <view class=\"group_3 flex-row\">\n        <text class=\"text_3\">预约项目</text>\n        <text class=\"text_4\">推荐技师</text>\n        <view class=\"image-text_1 flex-row justify-between\">\n          <text class=\"text-group_1\">切换</text>\n          <image\n            class=\"thumbnail_5\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png\"\n          />\n        </view>\n      </view>\n      <view class=\"group_4 flex-col\"></view>\n      <view class=\"list_1 flex-col\">\n        <view\n          class=\"list-items_1 flex-row justify-between\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <view class=\"image-text_2 flex-row\">\n            <image\n              class=\"image_3\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage0\"\n            />\n            <view class=\"text-group_2 flex-col\">\n              <text class=\"text_5\" v-html=\"item.lanhutext0\"></text>\n              <text class=\"text_6\" v-html=\"item.lanhutext1\"></text>\n              <view class=\"box_1 flex-row justify-between\">\n                <view class=\"text-wrapper_1\">\n                  <text class=\"text_7\" v-html=\"item.lanhutext2\"></text>\n                  <text class=\"text_8\" v-html=\"item.lanhutext3\"></text>\n                </view>\n                <text class=\"text_9\" v-html=\"item.lanhutext4\"></text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_2 flex-col\">\n              <text class=\"text_10\" v-html=\"item.lanhutext5\"></text>\n            </view>\n            <view class=\"block_3 flex-row\">\n              <view class=\"group_5 flex-col\">\n                <view class=\"block_4 flex-col\"></view>\n              </view>\n              <text class=\"text_11\" v-html=\"item.lanhutext6\"></text>\n            </view>\n            <view class=\"block_5 flex-row\">\n              <view class=\"image-text_3 flex-row justify-between\">\n                <view class=\"group_6 flex-col\"></view>\n                <text class=\"text-group_3\" v-html=\"item.lanhutext7\"></text>\n              </view>\n            </view>\n          </view>\n          <view class=\"text-wrapper_3 flex-col\">\n            <text class=\"text_12\" v-html=\"item.lanhutext8\"></text>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"group_7 flex-col justify-end\">\n      <text class=\"text_13\">尊享spa奇遇店铺</text>\n      <view class=\"box_2 flex-row justify-between\">\n        <view class=\"image-text_4 flex-row justify-between\">\n          <view class=\"group_8 flex-col\"></view>\n          <text class=\"text-group_4\">营业执照：1</text>\n        </view>\n        <image\n          class=\"thumbnail_6\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG804a5aeaa27993298a6fa9dcdf2b2972.png\"\n        />\n      </view>\n      <view class=\"image-text_5 flex-row justify-between\">\n        <image\n          class=\"thumbnail_7\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png\"\n        />\n        <text class=\"text-group_5\">营业时间：00:00-23:59</text>\n      </view>\n      <view class=\"box_3 flex-row\">\n        <view class=\"image-text_6 flex-row justify-between\">\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG963de7e8d6afeddae849b9f675f92506.png\"\n          />\n          <text class=\"text-group_6\">重庆</text>\n        </view>\n        <view class=\"text-wrapper_4 flex-col\">\n          <text class=\"text_14\">复制</text>\n        </view>\n        <view class=\"image-wrapper_1 flex-col\">\n          <image\n            class=\"thumbnail_9\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNG4d8c19ed24aff651d9e8416fcc37b826.png\"\n          />\n        </view>\n        <image\n          class=\"thumbnail_10\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shangjiaxiangqing_1/FigmaDDSSlicePNGb668c36ffd61328bf9025fd82d819e7d.png\"\n        />\n      </view>\n      <view class=\"text-wrapper_5 flex-col\">\n        <text class=\"text_15\">\n          为保障用户权益，下单后服务者如有涉嫌欺诈消费者或推销违规服务，请直接联系平台投诉电话:4008607172\n        </text>\n      </view>\n      <view class=\"box_4 flex-col\"></view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG31da488d12d11503c7344795ff172a3f.png',\n          lanhutext0: '狐狸舒适推拿',\n          lanhutext1: '肌肉舒张&nbsp;缓解腰肌劳损',\n          lanhutext2: '￥',\n          lanhutext3: '298',\n          lanhutext4: '￥482.00',\n          lanhutext5: '会员价',\n          lanhutext6: '5201已预约',\n          lanhutext7: '60分钟',\n          lanhutext8: '选择技师'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG31da488d12d11503c7344795ff172a3f.png',\n          lanhutext0: '狐狸舒适推拿',\n          lanhutext1: '肌肉舒张&nbsp;缓解腰肌劳损',\n          lanhutext2: '￥',\n          lanhutext3: '298',\n          lanhutext4: '￥482.00',\n          lanhutext5: '会员价',\n          lanhutext6: '5201已预约',\n          lanhutext7: '60分钟',\n          lanhutext8: '选择技师'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177574377\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}