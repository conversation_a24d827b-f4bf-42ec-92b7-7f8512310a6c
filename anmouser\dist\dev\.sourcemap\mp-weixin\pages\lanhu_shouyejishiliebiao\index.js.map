{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?51a9", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?d44a", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?82ae", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?bea4", "uni-app:///src/pages/lanhu_shouyejishiliebiao/index.vue", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?9216", "webpack:///./src/pages/lanhu_shouyejishiliebiao/index.vue?2dd3"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhuimage0", "lanhutext0", "technicianList", "id", "name", "status", "earliestTime", "rating", "serviceCount", "freeTravel", "comments", "favorites", "shopType", "avatar", "freeIcon", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA6D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF1CG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCyQtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,EACA;MACA;MACAC,cAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAX,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAX,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAX,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;MACA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACrXA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shouyejishiliebiao/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shouyejishiliebiao/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=926b01f8&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shouyejishiliebiao/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=926b01f8&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row justify-between\">\n        <text class=\"text_2\">雪狐到家</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"group_2 flex-col\">\n      <view class=\"box_3 flex-row\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <view class=\"section_1 flex-col\"></view>\n          <text class=\"text-group_1\">安心购</text>\n        </view>\n        <text class=\"text_3\">平台担保，无额外收费急速退款</text>\n        <view class=\"box_4 flex-row\">\n          <view class=\"image-text_2 flex-row justify-between\">\n            <text class=\"text-group_2\">默认城市</text>\n            <view class=\"block_1 flex-col\"></view>\n          </view>\n          <text class=\"text_4\">请输入要查找的项目</text>\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG4b0c31646ada352b81d926d484d6514a.png\"\n          />\n        </view>\n      </view>\n      <view class=\"box_5 flex-col\">\n        <view class=\"text-wrapper_1 flex-col\">\n          <text class=\"text_5\">\n            The&nbsp;fox&nbsp;has&nbsp;arrived&nbsp;home\n          </text>\n        </view>\n        <view class=\"box_6 flex-col\">\n          <text class=\"text_6\">\n            平台担保&nbsp;&nbsp;&nbsp;持证上岗&nbsp;&nbsp;&nbsp;爽约包赔&nbsp;&nbsp;&nbsp;收费透明\n          </text>\n          <text class=\"text_7\">还有推</text>\n          <view class=\"box_7 flex-col\"></view>\n          <view class=\"box_8 flex-col\">\n            <text class=\"text_8\">商家加盟&nbsp;&nbsp;共创共赢</text>\n            <view class=\"group_3 flex-row\">\n              <view class=\"text-group_3 flex-col justify-between\">\n                <text class=\"text_9\">利润收益丰富&nbsp;掌握无线发展可能</text>\n                <text class=\"text_10\">产品功能齐全&nbsp;信息综合全面</text>\n              </view>\n            </view>\n            <view class=\"group_4 flex-col\">\n              <view class=\"text-wrapper_2 flex-col\">\n                <text class=\"paragraph_1\">\n                  诚邀\n                  <br />\n                  加盟\n                </text>\n              </view>\n            </view>\n          </view>\n          <text class=\"text_11\">收入客观·时间自由·高额提成</text>\n        </view>\n      </view>\n      <view class=\"box_9 flex-row\">\n        <text class=\"text_12\">预约上门</text>\n        <text class=\"text_13\">推荐技师</text>\n        <text class=\"text_14\">到店服务</text>\n        <view class=\"image-text_3 flex-row justify-between\">\n          <text class=\"text-group_4\">切换</text>\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png\"\n          />\n        </view>\n      </view>\n      \n      <view class=\"box_10 flex-col\"></view>\n\n      <!--技师列表 栅格一行两列-->\n      <view class=\"technician-grid\">\n        <view class=\"technician-card\" v-for=\"technician in technicianList\" :key=\"technician.id\">\n          <!-- 技师头像背景区域 -->\n          <view class=\"card-avatar\">\n            <view class=\"time-badge\">\n              <view class=\"time-label-wrapper\">\n                <text class=\"time-label\">最早可约</text>\n              </view>\n              <text class=\"time-value\">{{ technician.earliestTime }}</text>\n            </view>\n          </view>\n\n          <!-- 技师信息卡片 -->\n          <view class=\"card-content\">\n            <!-- 技师姓名和状态 -->\n            <view class=\"technician-header\">\n              <text class=\"technician-name\">{{ technician.name }}</text>\n              <view class=\"status-badge\">\n                <text class=\"status-text\">{{ technician.status }}</text>\n              </view>\n            </view>\n\n            <!-- 评分和服务次数 -->\n            <view class=\"rating-section\">\n              <view class=\"rating-star\"></view>\n              <view class=\"service-info\">\n                <text class=\"rating-score\">{{ technician.rating }}</text>\n                <text class=\"service-count\">已服务{{ technician.serviceCount }}单</text>\n              </view>\n            </view>\n\n            <!-- 出行费用 -->\n            <view class=\"travel-fee\">\n              <image\n                class=\"fee-icon\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"technician.freeIcon\"\n              />\n              <text class=\"fee-text\">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"action-buttons\">\n              <view class=\"btn-secondary\">\n                <text class=\"btn-text\">更多照片</text>\n              </view>\n              <view class=\"btn-primary\">\n                <text class=\"btn-text\">立即预约</text>\n              </view>\n            </view>\n\n            <!-- 底部图标信息 -->\n            <view class=\"bottom-info\">\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.comments }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.favorites }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.shopType }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      <!--技师列表 栅格一行两列-->\n\n      <view class=\"box_21 flex-row justify-around\">\n        <view class=\"block_3 flex-col justify-between\">\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/ea5d801051eb486fb31451ca56fdf4c3_mergeImage.png\"\n          />\n          <text class=\"text_35\">首页</text>\n        </view>\n        <view class=\"image-text_10 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/bd8fcc774f4b4880abfc898767111c3e_mergeImage.png\"\n          />\n          <text class=\"text-group_11\">技师</text>\n        </view>\n        <view class=\"image-text_11 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/3481da0e40e24b8c95eaa5847a4aa0a9_mergeImage.png\"\n          />\n          <text class=\"text-group_12\">订单</text>\n        </view>\n        <view class=\"image-text_12 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/82ba36cd834d4da094a54aa824720880_mergeImage.png\"\n          />\n          <text class=\"text-group_13\">我的</text>\n        </view>\n        <image\n          class=\"label_5\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG52ab17b699dcd97c93a69999aa9aa3be.png\"\n        />\n      </view>\n      \n    </view>\n    <view class=\"group_15 flex-col\">\n      <view class=\"section_3 flex-row\">\n        <view class=\"image-text_13 flex-row justify-between\">\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png\"\n          />\n          <text class=\"text-group_14\">实名认证</text>\n        </view>\n        <view class=\"image-text_14 flex-row justify-between\">\n          <image\n            class=\"thumbnail_9\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNGa311c0aa4464ab4a12f421c8228cb8a0.png\"\n          />\n          <text class=\"text-group_15\">爽约包赔</text>\n        </view>\n        <view class=\"image-text_15 flex-row justify-between\">\n          <image\n            class=\"thumbnail_10\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG94a26a412b6165a9dd1c6161d011fe1c.png\"\n          />\n          <text class=\"text-group_16\">超时秒退</text>\n        </view>\n        <view class=\"image-text_16 flex-row justify-between\">\n          <image\n            class=\"thumbnail_11\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNGe12dabfd56c3e4a4ac8b73a89a417089.png\"\n          />\n          <text class=\"text-group_17\">资质认证</text>\n        </view>\n      </view>\n      <view class=\"grid_1 flex-row\">\n        <view\n          class=\"image-text_17 flex-col justify-between\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <image\n            class=\"label_6\"\n            referrerpolicy=\"no-referrer\"\n            :src=\"item.lanhuimage0\"\n          />\n          <text class=\"text-group_18\" v-html=\"item.lanhutext0\"></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG60c9e11ea671e9071f10ec4fa81522a1.png',\n          lanhutext0: '养生健康'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG9f11741a21b235bfe1bb6589f4525928.png',\n          lanhutext0: '伴游伴玩'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe3af19da0f97379a81ec29a2a9ceaabc.png',\n          lanhutext0: '生活美容'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG5b04c16665581dc8eb7ff0beaf51b8fe.png',\n          lanhutext0: '家政维修'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGdbe146b7f68cb9d247a8920c57145588.png',\n          lanhutext0: '采耳专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGf455b9a4b2a2527551a2300706663739.png',\n          lanhutext0: '台球助教'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG4614c422961226f3f9e2bd57c296db6d.png',\n          lanhutext0: '女士专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe1cbf6c812a20e3d29e3d746a1e07bdc.png',\n          lanhutext0: '小儿推拿'\n        }\n      ],\n      // 技师列表数据\n      technicianList: [\n        {\n          id: 1,\n          name: '王艳艳',\n          status: '可预约',\n          earliestTime: '11:00',\n          rating: 5,\n          serviceCount: 498,\n          freeTravel: true,\n          comments: 0,\n          favorites: 0,\n          shopType: '商家',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177581324\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}