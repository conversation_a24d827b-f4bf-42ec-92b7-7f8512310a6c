{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/personalcenter/index.vue?2731", "webpack:///./src/pages/personalcenter/index.vue?0947", "webpack:///./src/pages/personalcenter/index.vue?cd82", "webpack:///./src/pages/personalcenter/index.vue?6375", "uni-app:///src/pages/personalcenter/index.vue", "webpack:///./src/pages/personalcenter/index.vue?e3f9", "webpack:///./src/pages/personalcenter/index.vue?073e"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "technicianList", "id", "name", "status", "earliestTime", "rating", "serviceCount", "freeTravel", "comments", "favorites", "shopType", "distance", "lanhuBg13", "lanhuimage1", "lanhuimage2", "avatar", "freeIcon", "tabsIndex", "layoutMode", "shopList", "shopImage", "shopName", "businessHours", "starIcon", "ratingInfo", "userInfo", "nickname", "level", "isVip", "orderInfo", "pendingPaymentCount", "balance", "couponCount", "constants", "computed", "memberStatusText", "memberButtonText", "formattedBalance", "wan", "toFixed", "concat", "onLoad", "loadLayoutMode", "methods", "toggleTabs", "index", "toggleLayout", "saveLayoutMode", "uni", "setStorageSync", "error", "console", "savedLayoutMode", "getStorageSync", "undefined", "toggleVipStatus", "updateNickname", "newNickname", "updateLevel", "newLevel", "updatePendingPaymentCount", "count", "updateBalance", "updateCouponCount", "clearPendingPayments", "addPendingPayment", "handleMemberClick", "navigateTo", "url"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAmD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFhCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC6pBtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,cAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAf,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,EACA;MACA;MACAC,SAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA,GACA;QACAC,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,GACA;QACAS,SAAA;QACAC,QAAA;QACAhB,MAAA;QACAiB,aAAA;QACAC,QAAA;QACAC,UAAA;QACArB,MAAA;QACAG,YAAA;QACAK,QAAA;MACA,EACA;MACA;MACAc,QAAA;QACAC,QAAA;QAAA;QACAC,KAAA;QAAA;QACAC,KAAA;MACA;MACA;MACAC,SAAA;QACAC,mBAAA;QAAA;QACAC,OAAA;QAAA;QACAC,WAAA;MACA;MACAC,SAAA;IACA;EACA;EACA;EACAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,YAAAV,QAAA,CAAAG,KAAA;IACA;IACA;IACAQ,gBAAA,WAAAA,iBAAA;MACA,YAAAX,QAAA,CAAAG,KAAA;IACA;IACA;IACAS,gBAAA,WAAAA,iBAAA;MACA,IAAAN,OAAA,QAAAF,SAAA,CAAAE,OAAA;MACA,IAAAA,OAAA;QACA;QACA,IAAAO,GAAA,IAAAP,OAAA,UAAAQ,OAAA;QACA,UAAAC,MAAA,CAAAF,GAAA;MACA;QACA;QACA,OAAAP,OAAA,CAAAQ,OAAA;MACA;IACA;EACA;EACAE,MAAA,WAAAA,OAAA;IACA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACA;IACAC,UAAA,WAAAA,WAAAC,KAAA;MACA,KAAA5B,SAAA,GAAA4B,KAAA;IACA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAA5B,UAAA,QAAAA,UAAA;MACA;MACA,KAAA6B,cAAA;IACA;IACA;IACAA,cAAA,WAAAA,eAAA;MACA;QACAC,GAAA,CAAAC,cAAA,8BAAA/B,UAAA;MACA,SAAAgC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IACA;IACAR,cAAA,WAAAA,eAAA;MACA;QACA,IAAAU,eAAA,GAAAJ,GAAA,CAAAK,cAAA;QACA,IAAAD,eAAA,WAAAA,eAAA,aAAAA,eAAA,KAAAE,SAAA;UACA,KAAApC,UAAA,GAAAkC,eAAA;QACA;MACA,SAAAF,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA;QACA,KAAAhC,UAAA;MACA;IACA;IACA;IACA;IACAqC,eAAA,WAAAA,gBAAA;MACA,KAAA9B,QAAA,CAAAG,KAAA,SAAAH,QAAA,CAAAG,KAAA;IACA;IACA;IACA4B,cAAA,WAAAA,eAAAC,WAAA;MACA,KAAAhC,QAAA,CAAAC,QAAA,GAAA+B,WAAA;IACA;IACA;IACAC,WAAA,WAAAA,YAAAC,QAAA;MACA,KAAAlC,QAAA,CAAAE,KAAA,GAAAgC,QAAA;IACA;IACA;IACA;IACAC,yBAAA,WAAAA,0BAAAC,KAAA;MACA,KAAAhC,SAAA,CAAAC,mBAAA,GAAA+B,KAAA;IACA;IACA;IACAC,aAAA,WAAAA,cAAA/B,OAAA;MACA,KAAAF,SAAA,CAAAE,OAAA,GAAAA,OAAA;IACA;IACA;IACAgC,iBAAA,WAAAA,kBAAAF,KAAA;MACA,KAAAhC,SAAA,CAAAG,WAAA,GAAA6B,KAAA;IACA;IACA;IACAG,oBAAA,WAAAA,qBAAA;MACA,KAAAnC,SAAA,CAAAC,mBAAA;IACA;IACA;IACAmC,iBAAA,WAAAA,kBAAA;MACA,KAAApC,SAAA,CAAAC,mBAAA;IACA;IACA;IACA;IACAoC,iBAAA,WAAAA,kBAAA;MACA,SAAAzC,QAAA,CAAAG,KAAA;QACA;QACAoB,GAAA,CAAAmB,UAAA;UACAC,GAAA;QACA;MACA;QACA;QACApB,GAAA,CAAAmB,UAAA;UACAC,GAAA;QACA;MACA;IACA;EACA;AACA,E;;;;;;;;;;;;;ACt3BA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/personalcenter/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/personalcenter/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=d2afb65e&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/personalcenter/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=d2afb65e&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/@dcloudio/uni-ui/lib/uni-icons/uni-icons\" */ \"@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <scroll-view class=\"page-scroll\" scroll-y=\"true\" enable-back-to-top=\"true\">\n    <view class=\"page flex-col\">\n    \n    <view class=\"group_2 flex-col\">\n      <view class=\"group_3 flex-col\">\n        <view class=\"group_4 flex-row justify-between\">\n          <view class=\"image-text_1 flex-row justify-between\">\n            <image\n              class=\"label_1\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNGeefb5f5aabd1316472c3ff1e925727e1.png\"\n            />\n            <view class=\"group_5 flex-col justify-between\">\n              <text class=\"text-group_1\">{{ userInfo.nickname }}</text><!--绑定变量-->\n              <view class=\"group_6 flex-row\">\n                <view class=\"block_1 flex-col\"></view>\n                <text class=\"text_3\">{{ userInfo.level }}</text><!--绑定变量-->\n              </view>\n            </view>\n          </view>\n          <image\n            class=\"image_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/FigmaDDSSlicePNG76647f6356a350a45ac947b45adc1593.png\"\n          />\n        </view>\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/personalcenter/FigmaDDSSlicePNGacfd7ad2ddafa8bae896d1520482e121.png\"\n        />\n        <view class=\"group_7 flex-col\">\n          <view class=\"group_8 flex-row\">\n            <view class=\"image-text_2 flex-row justify-between\">\n              <image\n                class=\"label_2\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/personalcenter/FigmaDDSSlicePNG4f12a4c3e5599b0184444dd7fc4d1980.png\"\n              />\n              <text class=\"text-group_2\">{{ memberStatusText }}</text><!--绑定变量 会员显示=你已开通会员 非会员显示=开通会员享受更多权益-->\n            </view>\n            <view class=\"text-wrapper_1 flex-col\" @click=\"handleMemberClick\">\n              <text class=\"text_4\">{{ memberButtonText }}</text><!--绑定变量 会员显示=查看会员 非会员显示=开通会员-->\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"group_9 flex-col\">\n      <view class=\"box_3 flex-row\">\n        <view class=\"image-text_3 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/ad35a5f68eff47fc899bd6b01eb6b33f_mergeImage.png\"\n          />\n          <text class=\"text-group_3\">关注</text>\n        </view>\n        <view class=\"image-text_4 flex-col justify-between\">\n          <image\n            class=\"image_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/0e3ecbaa34e94df2b75b5119b600af88_mergeImage.png\"\n          />\n          <text class=\"text-group_4\">收藏</text>\n        </view>\n        <view class=\"image-text_5 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/cf9e907a6404467f8df165bc0bff8d48_mergeImage.png\"\n          />\n          <text class=\"text-group_5\">动态</text>\n        </view>\n        <view class=\"image-text_6 flex-col justify-between\">\n          <view class=\"box_4 flex-col\">\n            <view class=\"image-wrapper_1 flex-col\">\n              <image\n                class=\"label_5\"\n                referrerpolicy=\"no-referrer\"\n                src=\"/static/personalcenter/FigmaDDSSlicePNGf409f9ed6006935444c0c8f348089697.png\"\n              />\n            </view>\n          </view>\n          <text class=\"text-group_6\">门店</text>\n        </view>\n        <view class=\"image-text_7 flex-col justify-between\">\n          <image\n            class=\"label_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/019a4b9965db48baa4840c636bbee903_mergeImage.png\"\n          />\n          <text class=\"text-group_7\">地图找人</text>\n        </view>\n      </view>\n      <view class=\"box_5 flex-row\">\n        <view class=\"image-text_8 flex-col justify-between\">\n          <view class=\"image-wrapper_2 flex-col\">\n            <image\n              class=\"image_4\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNGbb150eb6a38fbfcbbf55aa117fd4cdf2.png\"\n            />\n          </view>\n          <text class=\"text-group_8\">分销经理</text>\n        </view>\n        <view class=\"image-text_9 flex-col justify-between\">\n          <image\n            class=\"label_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/8af0123c26ad4f29bf858e4a5d9a5439_mergeImage.png\"\n          />\n          <text class=\"text-group_9\">经纪人</text>\n        </view>\n        <view class=\"image-text_10 flex-col justify-between\">\n          <view class=\"image-wrapper_3 flex-col\">\n            <image\n              class=\"image_5\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNGf3485d71924eae3e1d200a94188db74a.png\"\n            />\n          </view>\n          <text class=\"text-group_10\">合作商</text>\n        </view>\n        <view class=\"image-text_11 flex-col justify-between\">\n          <view class=\"image-wrapper_4 flex-col\">\n            <image\n              class=\"label_8\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNG6fb7c1c25b5a60929b5bdbdc857d68bf.png\"\n            />\n          </view>\n          <text class=\"text-group_11\">招商加盟</text>\n        </view>\n        <view class=\"image-text_12 flex-col justify-between\">\n          <view class=\"image-wrapper_5 flex-col\">\n            <image\n              class=\"image_6\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNG18d04d9675774849820b4a9fd4dc267f.png\"\n            />\n          </view>\n          <text class=\"text-group_12\">地址管理</text>\n        </view>\n      </view>\n      <view class=\"box_6 flex-row\">\n        <view class=\"image-text_13 flex-col justify-between\">\n          <view class=\"image-wrapper_6 flex-col\">\n            <image\n              class=\"label_9\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNG4c9c11407c3c41a31cdee2e6b3b19a07.png\"\n            />\n          </view>\n          <text class=\"text-group_13\">问题反馈</text>\n        </view>\n        <view class=\"image-text_14 flex-col justify-between\">\n          <view class=\"image-wrapper_7 flex-col\">\n            <image\n              class=\"label_10\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNG053711ba7acd0dd54d19fe36db0d9b41.png\"\n            />\n          </view>\n          <text class=\"text-group_14\">联系客服</text>\n        </view>\n        <view class=\"image-text_15 flex-col justify-between\">\n          <view class=\"image-wrapper_8 flex-col\">\n            <image\n              class=\"label_11\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNGbc1290565abe84c506c2266e23ecf488.png\"\n            />\n          </view>\n          <text class=\"text-group_15\">切换管理员</text>\n        </view>\n        <view class=\"image-text_16 flex-col justify-between\">\n          <image\n            class=\"image_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/b179b2f3abd74f12934c0de60d17d968_mergeImage.png\"\n          />\n          <text class=\"text-group_16\">切换技师</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 项目列表 -->\n      <!-- tabs -->\n      <view class=\"group_7 flex-row\">\n        <view class=\"tabs-container\">\n          <view class=\"tab-item\">\n            <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 0 }\" @click=\"toggleTabs(0)\">预约上门</text>\n            <view class=\"tab-indicator\" v-if=\"tabsIndex === 0\"></view>\n          </view>\n          <view class=\"tab-item\">\n            <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 1 }\" @click=\"toggleTabs(1)\">推荐技师</text>\n            <view class=\"tab-indicator\" v-if=\"tabsIndex === 1\"></view>\n          </view>\n          <view class=\"tab-item\">\n            <text class=\"tab-text\" :class=\"{ 'tab-active': tabsIndex === 2 }\" @click=\"toggleTabs(2)\">到店服务</text>\n            <view class=\"tab-indicator\" v-if=\"tabsIndex === 2\"></view>\n          </view>\n        </view>\n        <view class=\"image-text_3 flex-row justify-between\" @click=\"toggleLayout\">\n          <text class=\"text-group_4\">切换</text>\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/userhome/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png\"\n          />\n        </view>\n      </view>\n      <!-- tabs -->\n\n      <!--技师列表 栅格一行两列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-grid\" v-if=\"tabsIndex === 1 && layoutMode === 0\" key=\"grid\">\n        <view class=\"technician-card\" v-for=\"technician in technicianList\" :key=\"technician.id\">\n          <!-- 技师头像背景区域 -->\n          <view class=\"card-avatar\">\n            <!-- 技师头像图片 -->\n            <image\n              class=\"technician-avatar\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"technician.avatar\"\n            />\n            <view class=\"time-badge\">\n              <view class=\"time-label-wrapper\">\n                <text class=\"time-label\">最早可约</text>\n              </view>\n              <text class=\"time-value\">{{ technician.earliestTime }}</text>\n            </view>\n          </view>\n\n          <!-- 技师信息卡片 -->\n          <view class=\"card-content\">\n            <!-- 技师姓名和状态 -->\n            <view class=\"technician-header\">\n              <text class=\"technician-name\">{{ technician.name }}</text>\n              <view class=\"status-badge\">\n                <text class=\"status-text\">{{ technician.status }}</text>\n              </view>\n            </view>\n\n            <!-- 评分和服务次数 -->\n            <view class=\"rating-section\">\n              <view class=\"rating-star\"></view>\n              <view class=\"service-info\">\n                <text class=\"rating-score\">{{ technician.rating }}</text>\n                <text class=\"service-count\">已服务{{ technician.serviceCount }}单</text>\n              </view>\n            </view>\n\n            <!-- 出行费用 -->\n            <view class=\"travel-fee\">\n              <image\n                class=\"fee-icon\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"technician.freeIcon\"\n              />\n              <text class=\"fee-text\">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>\n            </view>\n\n            <!-- 操作按钮 -->\n            <view class=\"action-buttons\">\n              <view class=\"btn-secondary\">\n                <text class=\"btn-text\">更多照片</text>\n              </view>\n              <view class=\"btn-primary\">\n                <text class=\"btn-text\">立即预约</text>\n              </view>\n            </view>\n\n            <!-- 底部图标信息 -->\n            <view class=\"bottom-info\">\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.comments }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.favorites }}</text>\n              </view>\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"16\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ technician.shopType }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行两列-->\n\n      <!--技师列表 栅格一行一列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"technician-list-container flex-col\" v-if=\"tabsIndex === 1 && layoutMode === 1\" key=\"list\">\n        <view\n          class=\"technician-list-item flex-col\"\n          v-for=\"(item, index) in technicianList\"\n          :key=\"index\"\n        >\n          <view class=\"technician-info-top flex-row\">\n            <image\n              class=\"technician-avatar-img\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.avatar\"\n            />\n            <view class=\"single-row-image flex-col justify-between\">\n              <view class=\"technician-name-row flex-row justify-between\">\n                <text class=\"technician-name-text\" >{{ item.name }}</text>\n                <view class=\"technician-photos-btn flex-col\">\n                  <text class=\"technician-photos-text\" > 更多照片 </text>\n                </view>\n              </view>\n              <view class=\"technician-rating-row flex-row justify-between\">\n                <view class=\"technician-rating-area flex-row justify-between\">\n                  <view class=\"technician-star-icon flex-col\"></view>\n                  <text class=\"technician-rating-text\" >{{ item.rating }}</text>\n                </view>\n                <text class=\"technician-service-text\" >已服务{{item.serviceCount}}单</text>\n              </view>\n            </view>\n            <view class=\"single-row-time flex-col justify-between\">\n              <view class=\"technician-time-wrapper flex-col\">\n                <text class=\"technician-time-text\" >最早可约：{{item.earliestTime}}</text>\n              </view>\n              <view class=\"technician-distance-area flex-row justify-between\">\n                <view class=\"single-row-distance flex-col\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n                </view>\n                <text class=\"technician-distance-text\" >{{item.distance}}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"technician-info-bottom flex-row\">\n            <view\n              class=\"technician-status-badge flex-col\"\n              :style=\"{ background: item.lanhuBg13 }\"\n            >\n              <text class=\"technician-status-text\" >{{ item.status }}</text>\n            </view>\n            <view class=\"bottom-info\">\n              <!--评论-->\n              <view class=\"info-item\">\n                <uni-icons type=\"chat\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.comments }}</text>\n              </view>\n\n              <!--收藏-->\n              <view class=\"info-item\">\n                <uni-icons type=\"star\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.favorites }}</text>\n              </view>\n\n              <!--商家-->\n              <view class=\"info-item\">\n                <uni-icons type=\"shop\" size=\"20\" color=\"#969696\"></uni-icons>\n                <text class=\"info-text\">{{ item.shopType }}</text>\n              </view>\n            </view>\n            <view class=\"technician-book-btn flex-col\">\n              <text class=\"technician-book-text\" >立即预约</text>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--技师列表 栅格一行一列-->\n\n      <!--到店服务栅格布局一行二列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"shop-grid\" v-if=\"tabsIndex === 2 && layoutMode === 0\" key=\"shop-grid\">\n        <view\n          class=\"shop-item flex-col\"\n          v-for=\"(shop, index) in shopList\"\n          :key=\"index\"\n        >\n          <image\n            class=\"shop-image\"\n            referrerpolicy=\"no-referrer\"\n            :src=\"shop.shopImage\"\n          />\n          <text class=\"shop-name\">{{ shop.shopName }}</text>\n          <view class=\"rating-section flex-row\">\n            <view class=\"star-bg flex-col\"></view>\n            <text class=\"rating-text\">{{ shop.rating }}</text>\n            <image\n              class=\"star-icon\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"shop.starIcon\"\n            />\n            <text class=\"hours-text\">{{ shop.businessHours }}</text>\n          </view>\n          <view class=\"distance-section flex-row justify-between\">\n            <image\n              class=\"location-icon\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG5a4163fb0783a530dfe7050cf95e5f4c.png\"\n            />\n            <text class=\"distance-text\">{{ shop.distance }}</text>\n          </view>\n          <view class=\"status-section flex-row justify-between\">\n            <view class=\"status-wrapper flex-col\">\n              <text class=\"status-text\">{{ shop.status }}</text>\n            </view>\n            <view class=\"service-wrapper flex-col\">\n              <text class=\"service-text\">{{ shop.serviceCount }}</text>\n            </view>\n          </view>\n          <view class=\"rating-info-wrapper flex-col\">\n            <text class=\"rating-info-text\">\n              {{ shop.ratingInfo }}\n            </text>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--到店服务栅格布局一行二列-->\n\n      <!--到店服务一行一列-->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"shop-service-list flex-col\" v-if=\"tabsIndex === 2 && layoutMode === 1\" key=\"shop-list\">\n        <view\n          class=\"shop-service-item flex-row\"\n          v-for=\"(item, index) in shopList\"\n          :key=\"index\"\n        >\n          <view class=\"shop-service-main flex-row justify-between\">\n            <image\n              class=\"shop-service-image\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.shopImage\"\n            />\n            <view class=\"shop-service-info flex-col\">\n              <view class=\"shop-service-header flex-col justify-between\">\n                <text class=\"shop-service-name\">{{ item.shopName }}</text>\n                <view class=\"shop-service-rating-time flex-row justify-between\">\n                  <text class=\"shop-service-rating\">{{ item.rating }}</text>\n                  <text class=\"shop-service-hours\">{{ item.businessHours }}</text>\n                </view>\n              </view>\n              <view class=\"shop-service-divider flex-col\"></view>\n              <image\n                class=\"shop-service-star\"\n                referrerpolicy=\"no-referrer\"\n                :src=\"item.starIcon\"\n              />\n              <view class=\"shop-service-rating-info flex-col\">\n                <text class=\"shop-service-rating-text\">{{ item.ratingInfo }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"shop-service-status flex-col\">\n            <text class=\"shop-service-status-text\">{{ item.status }}</text>\n          </view>\n          <view class=\"shop-service-details flex-col justify-between\">\n            <view class=\"shop-service-count flex-col\">\n              <text class=\"shop-service-count-text\">{{ item.serviceCount }}</text>\n            </view>\n            <view class=\"shop-service-distance flex-row justify-between\">\n              <view class=\"single-row-distance flex-col\">\n                  <uni-icons type=\"location\" size=\"16\" color=\"#0BCE94\"></uni-icons>\n              </view>\n              <text class=\"shop-service-distance-text\">{{ item.distance }}</text>\n            </view>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!--到店服务一行一列-->\n\n      <!-- 预约上门列表 -->\n      <transition name=\"fade-slide\" mode=\"out-in\">\n        <view class=\"list_1 flex-col\" v-if=\"tabsIndex === 0\" key=\"appointment-list\">\n        <view\n          class=\"list-items_1 flex-row justify-between\"\n          v-for=\"(item, index) in loopData0\"\n          :key=\"index\"\n        >\n          <view class=\"image-text_4 flex-row\">\n            <image\n              class=\"image_2\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage0\"\n            />\n            <view class=\"text-group_5 flex-col\">\n              <text class=\"text_15\">{{ item.lanhutext0 }}</text>\n              <text class=\"text_16\">{{ item.lanhutext1 }}</text>\n              <view class=\"section_2 flex-row justify-between\">\n                <view class=\"text-wrapper_3\">\n                  <text class=\"text_17\">{{ item.lanhutext2 }}</text>\n                  <text class=\"text_18\">{{ item.lanhutext3 }}</text>\n                </view>\n                <text class=\"text_19\">{{ item.lanhutext4 }}</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_4 flex-col\">\n              <text class=\"text_20\">{{ item.lanhutext5 }}</text>\n            </view>\n            <view class=\"box_4 flex-row\">\n              <view class=\"block_5 flex-col\">\n                <view class=\"section_3 flex-col\"></view>\n              </view>\n              <text class=\"text_21\">{{ item.lanhutext6 }}</text>\n            </view>\n            <view class=\"box_5 flex-row\">\n              <view class=\"image-text_5 flex-row justify-between\">\n                <view class=\"group_9 flex-col\"></view>\n                <text class=\"text-group_6\">{{ item.lanhutext7 }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"text-wrapper_5 flex-col\">\n            <text class=\"text_22\">{{ item.lanhutext8 }}</text>\n          </view>\n        </view>\n        </view>\n      </transition>\n      <!-- 预约上门列表 -->\n    <!-- 项目列表 -->\n\n    <!--底部导航-->\n    <view class=\"group_15 flex-row justify-around\">\n      <view class=\"image-text_20 flex-col justify-between\">\n        <image\n          class=\"label_12\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/personalcenter/FigmaDDSSlicePNG3a64844a13b7e0453d08677530393353.png\"\n        />\n        <text class=\"text-group_20\">首页</text>\n      </view>\n      <view class=\"image-text_21 flex-col justify-between\">\n        <image\n          class=\"label_13\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/personalcenter/99b6f0d4a9324f1db5762929120e3b3b_mergeImage.png\"\n        />\n        <text class=\"text-group_21\">技师</text>\n      </view>\n      <view class=\"image-text_22 flex-col justify-between\">\n        <image\n          class=\"label_14\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/personalcenter/664bdfde0f9f41f4abfc8c10d56a84fe_mergeImage.png\"\n        />\n        <text class=\"text-group_22\">订单</text>\n      </view>\n      <view class=\"image-text_23 flex-col justify-between\">\n        <image\n          class=\"label_15\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/personalcenter/90b3339871e04d31b896e2941512d032_mergeImage.png\"\n        />\n        <text class=\"text-group_23\">我的</text>\n      </view>\n    </view>\n    <!--底部导航-->\n\n    <view class=\"group_16 flex-col\">\n      <view class=\"box_8 flex-col\">\n        <view class=\"block_5 flex-row\">\n          <text class=\"text_16\">我的订单</text>\n          <text class=\"text_17\">全部订单</text>\n          <image\n            class=\"thumbnail_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/FigmaDDSSlicePNG44dd198f6c521802a160986b9f6b7bda.png\"\n          />\n        </view>\n        <view class=\"block_6 flex-row\">\n          <view class=\"image-text_24 flex-col justify-between\">\n            <view class=\"box_9 flex-col\">\n              <view class=\"text-wrapper_5 flex-col\" v-if=\"orderInfo.pendingPaymentCount > 0\">\n                <text class=\"text_18\">{{ orderInfo.pendingPaymentCount }}</text>\n              </view>\n            </view>\n            <text class=\"text-group_24\">待支付</text>\n          </view>\n          <view class=\"image-text_25 flex-col justify-between\">\n            <image\n              class=\"label_16\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/1e6e9705c535475d9458e11e09c31c8c_mergeImage.png\"\n            />\n            <text class=\"text-group_25\">待服务</text>\n          </view>\n          <view class=\"image-text_26 flex-col justify-between\">\n            <image\n              class=\"label_17\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/7025ea378cab49968e49354ae6d220a6_mergeImage.png\"\n            />\n            <text class=\"text-group_26\">服务中</text>\n          </view>\n          <view class=\"image-text_27 flex-col justify-between\">\n            <image\n              class=\"label_18\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/ba78a2d58e5e40f59eb3dcd1815589f4_mergeImage.png\"\n            />\n            <text class=\"text-group_27\">待评价</text>\n          </view>\n          <view class=\"image-text_28 flex-col justify-between\">\n            <image\n              class=\"label_19\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/personalcenter/FigmaDDSSlicePNGc4edb88dea16f6429c06c4faa0b431ce.png\"\n            />\n            <text class=\"text-group_28\">退款/售后</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_10 flex-row\">\n        <view class=\"image-text_29 flex-row justify-between\">\n          <view class=\"group_17 flex-col\">\n            <view class=\"group_18 flex-col\">\n              <view class=\"box_11 flex-col\"></view>\n            </view>\n          </view>\n          <view class=\"text-group_29 flex-col justify-between\">\n            <text class=\"text_19\">邀请好友获好礼</text>\n            <text class=\"text_20\">不止于按摩&nbsp;还有推拿&nbsp;做自己</text>\n          </view>\n        </view>\n        <view class=\"text-wrapper_6 flex-col\">\n          <text class=\"text_21\">立即推荐</text>\n        </view>\n      </view>\n      <view class=\"box_12 flex-row\">\n        <view class=\"box_13 flex-row\">\n          <view class=\"image-text_30 flex-row justify-between\">\n            <view class=\"text-group_30 flex-col justify-between\">\n              <text class=\"text_22\">我的余额</text>\n              <text class=\"text_23\">{{ formattedBalance }}</text>\n            </view>\n            <view class=\"text-wrapper_7 flex-col\">\n              <text class=\"text_24\">充值</text>\n            </view>\n            <view class=\"section_1 flex-col\"></view>\n          </view>\n        </view>\n        <view class=\"box_14 flex-row\">\n          <view class=\"image-text_31 flex-row justify-between\">\n            <view class=\"text-group_31 flex-col justify-between\">\n              <text class=\"text_25\">我的优惠券</text>\n              <view class=\"text-wrapper_8\">\n                <text class=\"text_26\">{{ orderInfo.couponCount }}</text>\n                <text class=\"text_27\">张</text>\n              </view>\n            </view>\n            <view class=\"text-wrapper_9 flex-col\">\n              <text class=\"text_28\">查看</text>\n            </view>\n          </view>\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/personalcenter/FigmaDDSSlicePNG7b7edc4d6a6092c372232723f3d7f451.png\"\n          />\n        </view>\n      </view>\n    </view>\n  </view>\n  </scroll-view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      // 技师列表数据\n      technicianList: [\n        {\n          id: 1,\n          name: '王艳艳',\n          status: '可预约',\n          earliestTime: '11:00',\n          rating: 5,\n          serviceCount: 489,\n          freeTravel: true,\n          comments: 0,\n          favorites: 0,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '不可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(153, 153, 153, 1)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        }\n      ],\n      // 控制技师列表显示\n      tabsIndex: 0,\n      // 控制技师列表布局模式：0=一行两列，1=一行一列\n      layoutMode: 0,\n      //店铺列表\n      shopList: [\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '爱上你spa按摩...',\n          rating: '5',\n          businessHours: '00:00-12:15',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 11%  接单率 11%',\n          status: '营业中',\n          serviceCount: '1+次服务',\n          distance: '0.26KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '按摩足浴店新...',\n          rating: '5',\n          businessHours: '00:00-12:15',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 11%  接单率 11%',\n          status: '营业中',\n          serviceCount: '1+次服务',\n          distance: '0.26KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '康复理疗中心...',\n          rating: '4',\n          businessHours: '09:00-22:00',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 95%  接单率 88%',\n          status: '营业中',\n          serviceCount: '50+次服务',\n          distance: '1.2KM'\n        },\n        {\n          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',\n          shopName: '康复理疗中心...',\n          rating: '4',\n          businessHours: '09:00-22:00',\n          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',\n          ratingInfo: '好评率 95%  接单率 88%',\n          status: '营业中',\n          serviceCount: '50+次服务',\n          distance: '1.2KM'\n        }\n      ],\n      // AUGMENT: 用户信息相关数据\n      userInfo: {\n        nickname: '爱莫能助', // 用户昵称\n        level: 1, // 用户等级\n        isVip: false // 是否为会员\n      },\n      // AUGMENT: 订单和财务相关数据\n      orderInfo: {\n        pendingPaymentCount: 1, // 待支付订单数量\n        balance: 2354.35, // 账户余额\n        couponCount: 2 // 优惠券数量\n      },\n      constants: {}\n    };\n  },\n  // AUGMENT: 计算属性\n  computed: {\n    // 会员状态文本\n    memberStatusText() {\n      return this.userInfo.isVip ? '你已开通会员' : '开通会员享受更多权益';\n    },\n    // 会员按钮文本\n    memberButtonText() {\n      return this.userInfo.isVip ? '查看会员' : '开通会员';\n    },\n    // 格式化余额显示\n    formattedBalance() {\n      const balance = this.orderInfo.balance;\n      if (balance >= 10000) {\n        // 大于等于1万时，显示为 \"X.X万\"\n        const wan = (balance / 10000).toFixed(1);\n        return `${wan}万`;\n      } else {\n        // 小于1万时，显示原始数字，保留两位小数\n        return balance.toFixed(2);\n      }\n    }\n  },\n  onLoad() {\n    // 页面加载时读取缓存的布局模式\n    this.loadLayoutMode();\n  },\n  methods: {\n    // 切换技师列表显示状态\n    toggleTabs(index) {\n      this.tabsIndex = index;\n    },\n    // 切换技师列表布局模式\n    toggleLayout() {\n      this.layoutMode = this.layoutMode === 0 ? 1 : 0;\n      // 保存布局模式到缓存\n      this.saveLayoutMode();\n    },\n    // 保存布局模式到本地缓存\n    saveLayoutMode() {\n      try {\n        uni.setStorageSync('technicianLayoutMode', this.layoutMode);\n      } catch (error) {\n        console.error('保存布局模式失败:', error);\n      }\n    },\n    // 从本地缓存读取布局模式\n    loadLayoutMode() {\n      try {\n        const savedLayoutMode = uni.getStorageSync('technicianLayoutMode');\n        if (savedLayoutMode !== '' && savedLayoutMode !== null && savedLayoutMode !== undefined) {\n          this.layoutMode = savedLayoutMode;\n        }\n      } catch (error) {\n        console.error('读取布局模式失败:', error);\n        // 读取失败时使用默认值\n        this.layoutMode = 0;\n      }\n    },\n    // AUGMENT: 用户信息相关方法\n    // 切换会员状态（用于测试）\n    toggleVipStatus() {\n      this.userInfo.isVip = !this.userInfo.isVip;\n    },\n    // 更新用户昵称\n    updateNickname(newNickname) {\n      this.userInfo.nickname = newNickname;\n    },\n    // 更新用户等级\n    updateLevel(newLevel) {\n      this.userInfo.level = newLevel;\n    },\n    // AUGMENT: 订单和财务相关方法\n    // 更新待支付订单数量\n    updatePendingPaymentCount(count) {\n      this.orderInfo.pendingPaymentCount = count;\n    },\n    // 更新账户余额\n    updateBalance(balance) {\n      this.orderInfo.balance = balance;\n    },\n    // 更新优惠券数量\n    updateCouponCount(count) {\n      this.orderInfo.couponCount = count;\n    },\n    // 模拟清空待支付订单（用于测试徽章隐藏）\n    clearPendingPayments() {\n      this.orderInfo.pendingPaymentCount = 0;\n    },\n    // 模拟添加待支付订单（用于测试徽章显示）\n    addPendingPayment() {\n      this.orderInfo.pendingPaymentCount += 1;\n    },\n    // AUGMENT: 会员相关方法\n    // 处理会员按钮点击\n    handleMemberClick() {\n      if (this.userInfo.isVip) {\n        // 已是会员，跳转到会员详情页\n        uni.navigateTo({\n          url: '/pages/membershipPage/index'\n        });\n      } else {\n        // 非会员，跳转到开通会员页\n        uni.navigateTo({\n          url: '/pages/membershipPage/index'\n        });\n      }\n    }\n  }\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177581510\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}