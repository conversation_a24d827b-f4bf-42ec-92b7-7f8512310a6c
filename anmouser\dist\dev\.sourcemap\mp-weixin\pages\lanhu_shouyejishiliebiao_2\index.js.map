{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?feaf", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?db82", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?5673", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?af98", "uni-app:///src/pages/lanhu_shouyejishiliebiao_2/index.vue", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?fd2a", "webpack:///./src/pages/lanhu_shouyejishiliebiao_2/index.vue?6d52"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "technicianList", "id", "name", "status", "earliestTime", "rating", "serviceCount", "freeTravel", "comments", "favorites", "shopType", "distance", "lanhuBg13", "lanhuimage1", "lanhuimage2", "avatar", "freeIcon", "loopData0", "lanhuimage0", "lanhutext0", "lanhutext1", "lanhutext2", "lanhutext3", "lanhutext4", "lanhutext5", "lanhutext6", "lanhutext7", "lanhutext8", "lanhutext9", "lanhutext10", "loopData1", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA+D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAF5CG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCC2Qtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA,GACA;QACAC,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,GACA;QACAf,EAAA;QACAC,IAAA;QACAC,MAAA;QACAC,YAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,WAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;MACA,EACA;MACAC,SAAA,GACA;QACAC,WAAA,EACA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAZ,SAAA;QACAa,UAAA;QACAZ,WAAA;QACAa,UAAA;QACAC,UAAA;QACAb,WAAA;QACAc,UAAA;QACAC,WAAA;MACA,EACA;MACAC,SAAA,GACA;QACAZ,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,GACA;QACAD,WAAA,EACA;QACAC,UAAA;MACA,EACA;MACAY,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACtXA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shouyejishiliebiao_2/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shouyejishiliebiao_2/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4c5b70b7&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shouyejishiliebiao_2/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4c5b70b7&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row justify-between\">\n        <text class=\"text_2\">雪狐到家</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"group_2 flex-col\">\n      <view class=\"group_3 flex-row\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <view class=\"section_1 flex-col\"></view>\n          <text class=\"text-group_1\">安心购</text>\n        </view>\n        <text class=\"text_3\">平台担保，无额外收费急速退款</text>\n        <view class=\"group_4 flex-row\">\n          <view class=\"image-text_2 flex-row justify-between\">\n            <text class=\"text-group_2\">默认城市</text>\n            <view class=\"box_3 flex-col\"></view>\n          </view>\n          <text class=\"text_4\">请输入要查找的项目</text>\n          <image\n            class=\"label_1\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG4b0c31646ada352b81d926d484d6514a.png\"\n          />\n        </view>\n      </view>\n      <view class=\"group_5 flex-col\">\n        <view class=\"text-wrapper_1 flex-col\">\n          <text class=\"text_5\">\n            The&nbsp;fox&nbsp;has&nbsp;arrived&nbsp;home\n          </text>\n        </view>\n        <view class=\"box_4 flex-col\">\n          <text class=\"text_6\">\n            平台担保&nbsp;&nbsp;&nbsp;持证上岗&nbsp;&nbsp;&nbsp;爽约包赔&nbsp;&nbsp;&nbsp;收费透明\n          </text>\n          <text class=\"text_7\">还有推</text>\n          <view class=\"box_5 flex-col\"></view>\n          <view class=\"box_6 flex-col\">\n            <text class=\"text_8\">商家加盟&nbsp;&nbsp;共创共赢</text>\n            <view class=\"box_7 flex-row\">\n              <view class=\"text-group_3 flex-col justify-between\">\n                <text class=\"text_9\">利润收益丰富&nbsp;掌握无线发展可能</text>\n                <text class=\"text_10\">产品功能齐全&nbsp;信息综合全面</text>\n              </view>\n            </view>\n            <view class=\"box_8 flex-col\">\n              <view class=\"text-wrapper_2 flex-col\">\n                <text class=\"paragraph_1\">\n                  诚邀\n                  <br />\n                  加盟\n                </text>\n              </view>\n            </view>\n          </view>\n          <text class=\"text_11\">收入客观·时间自由·高额提成</text>\n        </view>\n      </view>\n      <view class=\"group_6 flex-row\">\n        <text class=\"text_12\">预约上门</text>\n        <text class=\"text_13\">推荐技师</text>\n        <text class=\"text_14\">到店服务</text>\n        <view class=\"image-text_3 flex-row justify-between\">\n          <text class=\"text-group_4\">切换</text>\n          <image\n            class=\"thumbnail_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png\"\n          />\n        </view>\n      </view>\n      <view class=\"group_7 flex-col\"></view>\n\n      <!--技师列表 栅格一行一列-->\n      <view class=\"technician-list-container flex-col\">\n        <view\n          class=\"technician-list-item flex-col\"\n          v-for=\"(item, index) in technicianList\"\n          :key=\"index\"\n        >\n          <view class=\"technician-info-top flex-row\">\n            <image\n              class=\"image_2\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.avatar\"\n            />\n            <view class=\"single-row-image flex-col justify-between\">\n              <view class=\"box_9 flex-row justify-between\">\n                <text class=\"text_15\" >{{ item.name }}</text>\n                <view class=\"text-wrapper_3 flex-col\">\n                  <text class=\"text_16\" > 更多照片 </text>\n                </view>\n              </view>\n              <view class=\"box_10 flex-row justify-between\">\n                <view class=\"image-text_4 flex-row justify-between\">\n                  <view class=\"block_2 flex-col\"></view>\n                  <text class=\"text-group_5\" >{{ item.rating }}</text>\n                </view>\n                <text class=\"text_17\" >已服务{{item.serviceCount}}单</text>\n              </view>\n            </view>\n            <view class=\"single-row-time flex-col justify-between\">\n              <view class=\"text-wrapper_4 flex-col\">\n                <text class=\"text_18\" >最早可约：{{item.earliestTime}}</text>\n              </view>\n              <view class=\"image-text_5 flex-row justify-between\">\n                <view class=\"single-row-distance flex-col\"></view>\n                <text class=\"text-group_6\" >{{item.distance}}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"technician-info-bottom flex-row\">\n            <view\n              class=\"text-wrapper_5 flex-col\"\n              :style=\"{ background: item.lanhuBg13 }\"\n            >\n              <text class=\"text_19\" >{{ item.status }}</text>\n            </view>\n            <image\n              class=\"thumbnail_5\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage1\"\n            />\n            <text class=\"text_20\" >{{ item.comments }}</text>\n            <view class=\"group_11 flex-col\"></view>\n            <text class=\"text_21\" >{{ item.favorites }}</text>\n            <image\n              class=\"thumbnail_6\"\n              referrerpolicy=\"no-referrer\"\n              :src=\"item.lanhuimage2\"\n            />\n            <text class=\"text_22\" >{{item.shopType}}</text>\n            <view class=\"text-wrapper_6 flex-col\">\n              <text class=\"text_23\" >立即预约</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      <!--技师列表 栅格一行一列-->\n\n\n      <view class=\"group_12 flex-row justify-around\">\n        <view class=\"box_11 flex-col justify-between\">\n          <image\n            class=\"thumbnail_7\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/2ddc0c9cdc954466b1b10c9e93b44718_mergeImage.png\"\n          />\n          <text class=\"text_24\">首页</text>\n        </view>\n        <view class=\"image-text_6 flex-col justify-between\">\n          <image\n            class=\"label_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/dad19c4803584254a7e159bd899450f4_mergeImage.png\"\n          />\n          <text class=\"text-group_7\">技师</text>\n        </view>\n        <view class=\"image-text_7 flex-col justify-between\">\n          <image\n            class=\"label_3\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/fae48501547c4ab59ae390cda61c6e45_mergeImage.png\"\n          />\n          <text class=\"text-group_8\">订单</text>\n        </view>\n        <view class=\"image-text_8 flex-col justify-between\">\n          <image\n            class=\"label_4\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/711b7bc50e9445b3a1956a32f101d964_mergeImage.png\"\n          />\n          <text class=\"text-group_9\">我的</text>\n        </view>\n        <image\n          class=\"label_5\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG52ab17b699dcd97c93a69999aa9aa3be.png\"\n        />\n      </view>\n      <view class=\"text-wrapper_7 flex-row justify-between\">\n        <text class=\"text_25\">订单</text>\n        <text class=\"text_26\">我的</text>\n      </view>\n      <view class=\"text-wrapper_8 flex-row\">\n        <text class=\"text_27\">首页</text>\n        <text class=\"text_28\">技师</text>\n      </view>\n    </view>\n    <view class=\"group_13 flex-col\">\n      <view class=\"group_14 flex-row\">\n        <view class=\"image-text_9 flex-row justify-between\">\n          <image\n            class=\"thumbnail_8\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png\"\n          />\n          <text class=\"text-group_10\">实名认证</text>\n        </view>\n        <view class=\"image-text_10 flex-row justify-between\">\n          <image\n            class=\"thumbnail_9\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGa311c0aa4464ab4a12f421c8228cb8a0.png\"\n          />\n          <text class=\"text-group_11\">爽约包赔</text>\n        </view>\n        <view class=\"image-text_11 flex-row justify-between\">\n          <image\n            class=\"thumbnail_10\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNG644082427060ccd4f6f07c248397a799.png\"\n          />\n          <text class=\"text-group_12\">超时秒退</text>\n        </view>\n        <view class=\"image-text_12 flex-row justify-between\">\n          <image\n            class=\"thumbnail_11\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shouyejishiliebiao_2/FigmaDDSSlicePNGe12dabfd56c3e4a4ac8b73a89a417089.png\"\n          />\n          <text class=\"text-group_13\">资质认证</text>\n        </view>\n      </view>\n      <view class=\"grid_1 flex-row\">\n        <view\n          class=\"image-text_13 flex-col justify-between\"\n          v-for=\"(item, index) in loopData1\"\n          :key=\"index\"\n        >\n          <image\n            class=\"label_6\"\n            referrerpolicy=\"no-referrer\"\n            :src=\"item.lanhuimage0\"\n          />\n          <text class=\"text-group_14\" v-html=\"item.lanhutext0\"></text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      technicianList: [\n        {\n          id: 1,\n          name: '王艳艳',\n          status: '可预约',\n          earliestTime: '11:00',\n          rating: 5,\n          serviceCount: 489,\n          freeTravel: true,\n          comments: 0,\n          favorites: 0,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png'\n        },\n        {\n          id: 2,\n          name: '李美美',\n          status: '不可预约',\n          earliestTime: '12:00',\n          rating: 4.8,\n          serviceCount: 356,\n          freeTravel: false,\n          comments: 5,\n          favorites: 12,\n          shopType: '商家',\n          distance: '0.26km',\n          lanhuBg13: 'rgba(153, 153, 153, 1)',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',\n          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'\n        }\n      ],\n      loopData0: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6377fe1109b8069d54b3b65dd6dcbb33.png',\n          lanhutext0: '李三思',\n          lanhutext1: '更多照片',\n          lanhutext2: '5',\n          lanhutext3: '已服务498单',\n          lanhutext4: '最早可约：00::02',\n          lanhutext5: '0.26km',\n          lanhuBg13: 'rgba(11,206,148,1.000000)',\n          lanhutext6: '可预约',\n          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',\n          lanhutext7: '0',\n          lanhutext8: '0',\n          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',\n          lanhutext9: '商家',\n          lanhutext10: '立即预约'\n        }\n      ],\n      loopData1: [\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG60c9e11ea671e9071f10ec4fa81522a1.png',\n          lanhutext0: '养生健康'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG9f11741a21b235bfe1bb6589f4525928.png',\n          lanhutext0: '伴游伴玩'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe3af19da0f97379a81ec29a2a9ceaabc.png',\n          lanhutext0: '生活美容'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG5b04c16665581dc8eb7ff0beaf51b8fe.png',\n          lanhutext0: '家政维修'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGdbe146b7f68cb9d247a8920c57145588.png',\n          lanhutext0: '采耳专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGf455b9a4b2a2527551a2300706663739.png',\n          lanhutext0: '台球助教'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG4614c422961226f3f9e2bd57c296db6d.png',\n          lanhutext0: '女士专区'\n        },\n        {\n          lanhuimage0:\n            'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGe1cbf6c812a20e3d29e3d746a1e07bdc.png',\n          lanhutext0: '小儿推拿'\n        }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177579740\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}