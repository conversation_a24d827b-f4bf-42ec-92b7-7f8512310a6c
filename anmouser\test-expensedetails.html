<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>测试页面重命名</title>
</head>
<body>
    <h1>页面重命名测试</h1>
    <p>测试新的消费明细页面路径：</p>
    <ul>
        <li>原路径：pages/lanhu_chongzhijilu/index</li>
        <li>新路径：pages/expensedetails/index</li>
        <li>页面名称：expensedetails</li>
    </ul>
    
    <h2>验证项目：</h2>
    <ul>
        <li>✅ 页面目录已重命名：lanhu_chongzhijilu → expensedetails</li>
        <li>✅ pages.json 配置已更新</li>
        <li>✅ 静态资源路径已更新</li>
        <li>✅ 静态资源目录已移动</li>
        <li>✅ 样式文件中的行高问题已修复</li>
    </ul>
    
    <p>页面应该可以通过以下方式访问：</p>
    <code>uni.navigateTo({ url: '/pages/expensedetails/index' })</code>
</body>
</html>
