{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?644f", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?cd17", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?4eb6", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?9653", "uni-app:///src/pages/lanhu_wodekaquan/index.vue", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?b8ae", "webpack:///./src/pages/lanhu_wodekaquan/index.vue?daea"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAAqD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFlCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCmKtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;AC1KA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_wodekaquan/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_wodekaquan/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ca99e96&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_wodekaquan/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0ca99e96&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-col justify-between\">\n      <view class=\"box_1 flex-row\">\n        <text class=\"text_1\">12:30</text>\n        <image\n          class=\"thumbnail_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNGd7f3f0a8da68df76a531af0fb9fddfd7.png\"\n        />\n        <image\n          class=\"thumbnail_2\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n        />\n        <image\n          class=\"thumbnail_3\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n        />\n      </view>\n      <view class=\"box_2 flex-row\">\n        <image\n          class=\"thumbnail_4\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n        />\n        <text class=\"text_2\">我的卡券</text>\n        <image\n          class=\"image_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n        />\n      </view>\n    </view>\n    <view class=\"group_2 flex-col\">\n      <view class=\"box_3 flex-col justify-end\">\n        <view class=\"text-wrapper_1 flex-row justify-between\">\n          <text class=\"text_3\">待使用</text>\n          <text class=\"text_4\">已使用</text>\n          <text class=\"text_5\">已过期</text>\n        </view>\n        <view class=\"section_1 flex-row\">\n          <view class=\"section_2 flex-col\"></view>\n        </view>\n      </view>\n      <view class=\"text-wrapper_2 flex-col\">\n        <text class=\"text_6\">使用规则:399-20</text>\n        <text class=\"text_7\">优惠详情:399-20</text>\n        <text class=\"text_8\">限用服务:日式·尊享奢华</text>\n        <text class=\"text_9\">月上·轻奢私享</text>\n        <text class=\"text_10\">月上·舒适精油</text>\n      </view>\n      <view class=\"box_4 flex-row justify-between\">\n        <view class=\"section_3 flex-row\">\n          <view class=\"text-group_1 flex-col justify-between\">\n            <view class=\"text-wrapper_3\">\n              <text class=\"text_11\">￥</text>\n              <text class=\"text_12\">20.00</text>\n            </view>\n            <text class=\"text_13\">通用券</text>\n          </view>\n        </view>\n        <view class=\"section_4 flex-col\">\n          <view class=\"box_5 flex-row justify-between\">\n            <view class=\"text-group_2 flex-col justify-between\">\n              <text class=\"text_14\">满399-20可用</text>\n              <text class=\"text_15\">数量x1</text>\n            </view>\n            <view class=\"text-wrapper_4 flex-col\">\n              <text class=\"text_16\">立即使用</text>\n            </view>\n          </view>\n          <view class=\"box_6 flex-row justify-between\">\n            <text class=\"text_17\">有效期:2024.9.23&nbsp;17:21</text>\n            <image\n              class=\"thumbnail_5\"\n              referrerpolicy=\"no-referrer\"\n              src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNGed7624254381a8c4a98b43669029dae1.png\"\n            />\n          </view>\n        </view>\n      </view>\n      <view class=\"box_7 flex-row justify-between\">\n        <view class=\"section_5 flex-row\">\n          <view class=\"text-group_3 flex-col justify-between\">\n            <view class=\"text-wrapper_5\">\n              <text class=\"text_18\">￥</text>\n              <text class=\"text_19\">20.00</text>\n            </view>\n            <text class=\"text_20\">平台赠与券</text>\n          </view>\n        </view>\n        <view class=\"section_6 flex-col\">\n          <view class=\"box_8 flex-row justify-between\">\n            <view class=\"text-group_4 flex-col justify-between\">\n              <text class=\"text_21\">满0.00可用(无门栏)</text>\n              <text class=\"text_22\">平台通用</text>\n            </view>\n            <view class=\"section_7 flex-col\"></view>\n          </view>\n          <view class=\"text-wrapper_6 flex-row\">\n            <text class=\"text_23\">有效期:2024.9.23&nbsp;17:21</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_9 flex-row justify-between\">\n        <view class=\"box_10 flex-row\">\n          <view class=\"text-group_5 flex-col justify-between\">\n            <view class=\"text-wrapper_7\">\n              <text class=\"text_24\">￥</text>\n              <text class=\"text_25\">20.00</text>\n            </view>\n            <text class=\"text_26\">平台赠与券</text>\n          </view>\n        </view>\n        <view class=\"box_11 flex-col\">\n          <view class=\"box_12 flex-row justify-between\">\n            <view class=\"text-group_6 flex-col justify-between\">\n              <text class=\"text_27\">满0.00可用(无门栏)</text>\n              <text class=\"text_28\">平台通用</text>\n            </view>\n            <view class=\"text-wrapper_8 flex-col\">\n              <text class=\"text_29\">已使用</text>\n            </view>\n          </view>\n          <view class=\"text-wrapper_9 flex-row\">\n            <text class=\"text_30\">有效期:2024.9.23&nbsp;17:21</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"box_13 flex-row\">\n        <view class=\"text-group_7 flex-col justify-between\">\n          <view class=\"text-wrapper_10\">\n            <text class=\"text_31\">￥</text>\n            <text class=\"text_32\">20.00</text>\n          </view>\n          <text class=\"text_33\">通用券</text>\n        </view>\n      </view>\n      <view class=\"box_14 flex-col\">\n        <view class=\"block_1 flex-row justify-between\">\n          <view class=\"text-group_8 flex-col justify-between\">\n            <text class=\"text_34\">满399-20可用</text>\n            <text class=\"text_35\">数量x1</text>\n          </view>\n          <view class=\"text-wrapper_11 flex-col\">\n            <text class=\"text_36\">立即使用</text>\n          </view>\n        </view>\n        <view class=\"block_2 flex-row justify-between\">\n          <text class=\"text_37\">有效期:2024.9.23&nbsp;17:21</text>\n          <image\n            class=\"thumbnail_6\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_wodekaquan/FigmaDDSSlicePNG953f6fe6de372b686ac4d0ef789a12be.png\"\n          />\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177580911\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}