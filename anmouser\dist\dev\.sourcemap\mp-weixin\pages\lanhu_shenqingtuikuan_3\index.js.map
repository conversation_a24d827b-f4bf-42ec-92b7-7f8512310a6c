{"version": 3, "sources": ["uni-app:///src/main.js", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?0497", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?31da", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?eb5b", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?76bc", "uni-app:///src/pages/lanhu_shenqingtuikuan_3/index.vue", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?5199", "webpack:///./src/pages/lanhu_shenqingtuikuan_3/index.vue?0db7"], "names": ["require", "_vue", "_interopRequireDefault", "_index", "e", "__esModule", "default", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loopData0", "lanhutext0", "lanhutext1", "constants", "methods"], "mappings": ";;;;;;;;;;;;AAAAA,mBAAA;AACA,IAAAC,IAAA,GAAAC,sBAAA,CAAAF,mBAAA;AACA,IAAAG,MAAA,GAAAD,sBAAA,CAAAF,mBAAA;AAA4D,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAFzCG,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG7EC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACHhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmI;AACnI,gBAAgB,gJAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkc,CAAgB,8dAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCCwHtd;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,UAAA;QACAC,UAAA;MACA,GACA;QAAAD,UAAA;QAAAC,UAAA;MAAA,EACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;AACA,E;;;;;;;;;;;;ACtIA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,m5BAAG,EAAC,C;;;;;;;;;;;ACA77B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/lanhu_shenqingtuikuan_3/index.js", "sourcesContent": ["import 'uni-pages';wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/lanhu_shenqingtuikuan_3/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=71030ee5&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/lanhu_shenqingtuikuan_3/index.vue\"\nexport default component.exports", "export * from \"-!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--15-0!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=71030ee5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page flex-col\">\n    <view class=\"group_1 flex-row\">\n      <text class=\"text_1\">12:30</text>\n      <image\n        class=\"thumbnail_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNG01288eef35ac7e55af88547c7bcf2176.png\"\n      />\n      <image\n        class=\"thumbnail_2\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNG5386e23e17798b9096a433b239b703f0.png\"\n      />\n      <image\n        class=\"thumbnail_3\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNG5abe3a2953abf107d379c43fded5e38a.png\"\n      />\n    </view>\n    <view class=\"group_2 flex-row\">\n      <image\n        class=\"thumbnail_4\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNGb2c0f043641f22ede4595acd72b9777f.png\"\n      />\n      <text class=\"text_2\">同意退款</text>\n      <image\n        class=\"image_1\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNGb8b4476965cc16cde5ebd2a73f016520.png\"\n      />\n    </view>\n    <view class=\"group_3 flex-row\">\n      <view class=\"text-group_1 flex-col justify-between\">\n        <text class=\"text_3\">同意退款&nbsp;退款金额￥253.00</text>\n        <text class=\"text_4\">含车费452.00</text>\n      </view>\n      <view class=\"box_1 flex-row\">\n        <image\n          class=\"label_1\"\n          referrerpolicy=\"no-referrer\"\n          src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNGdde2ade77d54a0bc2cc44b3a31a6eeb7.png\"\n        />\n        <text class=\"text_5\">冉</text>\n        <text class=\"text_6\">18723755954</text>\n        <text class=\"text_7\">18723755954</text>\n        <text class=\"text_8\">重庆市渝中区民族路166号(临江门地铁站1号口...</text>\n      </view>\n    </view>\n    <view class=\"group_4 flex-col\">\n      <view class=\"block_1 flex-row justify-between\">\n        <view class=\"image-text_1 flex-row justify-between\">\n          <image\n            class=\"image_2\"\n            referrerpolicy=\"no-referrer\"\n            src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNGb7d6e462c2527f7ccdf81162f3f33bd8.png\"\n          />\n          <view class=\"text-group_2 flex-col\">\n            <text class=\"text_9\">狐狸舒适推拿</text>\n            <text class=\"text_10\">狐狸舒适推拿</text>\n            <text class=\"text_11\">服务技师&nbsp;&nbsp;冉</text>\n            <view class=\"text-wrapper_1\">\n              <text class=\"text_12\">￥</text>\n              <text class=\"text_13\">298.00</text>\n            </view>\n          </view>\n        </view>\n        <text class=\"text_14\">x1</text>\n      </view>\n      <view class=\"text-wrapper_2\">\n        <text class=\"text_15\">共</text>\n        <text class=\"text_16\">1</text>\n        <text class=\"text_17\">件商品&nbsp;合计:</text>\n        <text class=\"text_18\">￥321.00&nbsp;&nbsp;含车费:236.00</text>\n      </view>\n    </view>\n    <view class=\"text-wrapper_3 flex-col\">\n      <text class=\"text_19\">退款详情</text>\n    </view>\n    <view class=\"group_5 flex-row\">\n      <text class=\"text_20\">退款订单</text>\n      <text class=\"text_21\">154654135135435123</text>\n      <view class=\"text-wrapper_4 flex-col\">\n        <text class=\"text_22\">复制</text>\n      </view>\n    </view>\n    <view class=\"list_1 flex-col\">\n      <view\n        class=\"text-wrapper_5 flex-row\"\n        v-for=\"(item, index) in loopData0\"\n        :key=\"index\"\n      >\n        <text class=\"text_23\" v-html=\"item.lanhutext0\"></text>\n        <text class=\"text_24\" v-html=\"item.lanhutext1\"></text>\n      </view>\n    </view>\n    <view class=\"text-wrapper_6 flex-row justify-between\">\n      <text class=\"text_25\">审核人</text>\n      <text class=\"text_26\">admin</text>\n    </view>\n    <view class=\"text-wrapper_7 flex-col\">\n      <text class=\"text_27\">退款原因</text>\n      <text class=\"text_28\">\n        这是退款原因这是退款原因这是退款原因这是退款原因这是退款原因这是退款原因这是退款原因这是退款原因\n      </text>\n    </view>\n    <view class=\"text-wrapper_8 flex-col\">\n      <text class=\"text_29\">上传图片</text>\n    </view>\n    <view class=\"image-wrapper_1 flex-col\">\n      <image\n        class=\"image_3\"\n        referrerpolicy=\"no-referrer\"\n        src=\"/static/lanhu_shenqingtuikuan_3/FigmaDDSSlicePNG6d078048b721cb7f068f5ac326ce9d49.png\"\n      />\n    </view>\n  </view>\n</template>\n<script>\nexport default {\n  data() {\n    return {\n      loopData0: [\n        {\n          lanhutext0: '提交日期',\n          lanhutext1: '2024-12-21&nbsp;&nbsp;12:12-12:11'\n        },\n        { lanhutext0: '审核日期', lanhutext1: '2024-12-24&nbsp;07:12:14' }\n      ],\n      constants: {}\n    };\n  },\n  methods: {}\n};\n</script>\n<style lang='scss'>\n@import '../common/common.scss';\n@import './assets/style/index.rpx.scss';\n</style>\n", "import mod from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/loader.js??ref--9-oneOf-1-0!../../../node_modules/@vue/cli-service/node_modules/css-loader/dist/cjs.js??ref--9-oneOf-1-1!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-2!../../../node_modules/postcss-loader/src/index.js??ref--9-oneOf-1-3!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--9-oneOf-1-4!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--9-oneOf-1-5!../../../node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755177567876\n      var cssReload = require(\"F:/按摩项目/用户端/anmouser/node_modules/@vue/cli-service/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}